import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Clock, MapPin, Users, TriangleAlert as AlertTriangle } from 'lucide-react-native';
import Card from '@/components/ui/Card';
import Text from '@/components/ui/Text';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

export interface ScheduleItem {
  id: string;
  routeNumber: string;
  routeName: string;
  startLocation: string;
  endLocation: string;
  startTime: string;
  endTime: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  passengerCount?: number;
  notes?: string;
  hasWarning?: boolean;
}

interface ScheduleCardProps {
  item: ScheduleItem;
  onPress: (item: ScheduleItem) => void;
}

export function ScheduleCard({ item, onPress }: ScheduleCardProps) {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  // Status colors
  const getStatusColor = (status: ScheduleItem['status']) => {
    switch (status) {
      case 'scheduled':
        return Colors.primary[500];
      case 'in-progress':
        return Colors.accent[500];
      case 'completed':
        return Colors.success[500];
      case 'cancelled':
        return Colors.error[500];
      default:
        return Colors.gray[500];
    }
  };
  
  const getStatusText = (status: ScheduleItem['status']) => {
    switch (status) {
      case 'scheduled':
        return 'Scheduled';
      case 'in-progress':
        return 'In Progress';
      case 'completed':
        return 'Completed';
      case 'cancelled':
        return 'Cancelled';
      default:
        return status;
    }
  };

  return (
    <TouchableOpacity
      activeOpacity={0.8}
      onPress={() => onPress(item)}
      style={styles.touchable}
    >
      <Card variant="elevated">
        <View style={styles.header}>
          <View style={styles.routeContainer}>
            <View style={styles.routeTag}>
              <Text weight="bold" color={Colors.white}>
                {item.routeNumber}
              </Text>
            </View>
            <Text variant="h4" weight="semibold" numberOfLines={1}>
              {item.routeName}
            </Text>
          </View>
          <View style={[
            styles.statusBadge, 
            { backgroundColor: getStatusColor(item.status) + (isDark ? '30' : '15') }
          ]}>
            <Text 
              variant="caption" 
              weight="medium" 
              color={getStatusColor(item.status)}
            >
              {getStatusText(item.status)}
            </Text>
          </View>
        </View>

        <View style={styles.locationContainer}>
          <View style={styles.locationRow}>
            <MapPin size={16} color={Colors.primary[500]} />
            <Text variant="bodySmall" numberOfLines={1} style={styles.locationText}>
              From: {item.startLocation}
            </Text>
          </View>
          <View style={styles.locationRow}>
            <MapPin size={16} color={Colors.accent[500]} />
            <Text variant="bodySmall" numberOfLines={1} style={styles.locationText}>
              To: {item.endLocation}
            </Text>
          </View>
        </View>

        <View style={styles.divider} />

        <View style={styles.footer}>
          <View style={styles.footerItem}>
            <Clock size={16} color={Colors.gray[500]} />
            <Text variant="caption">
              {item.startTime} - {item.endTime}
            </Text>
          </View>

          {item.passengerCount !== undefined && (
            <View style={styles.footerItem}>
              <Users size={16} color={Colors.gray[500]} />
              <Text variant="caption">
                {item.passengerCount} passengers
              </Text>
            </View>
          )}

          {item.hasWarning && (
            <View style={styles.footerItem}>
              <AlertTriangle size={16} color={Colors.warning[500]} />
              <Text variant="caption" color={Colors.warning[500]}>
                Warning
              </Text>
            </View>
          )}
        </View>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  touchable: {
    marginBottom: Spacing.md,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  routeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  routeTag: {
    backgroundColor: Colors.primary[500],
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: Layout.borderRadius.sm,
    marginRight: Spacing.sm,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: Layout.borderRadius.sm,
  },
  locationContainer: {
    marginBottom: Spacing.md,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  locationText: {
    marginLeft: Spacing.xs,
    flex: 1,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.gray[200],
    marginBottom: Spacing.md,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    flexWrap: 'wrap',
  },
  footerItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.md,
    gap: 4,
  },
});

export default ScheduleCard;