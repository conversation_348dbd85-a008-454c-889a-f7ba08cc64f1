import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Play, Square, TriangleAlert as AlertTriangle, MessageCircle } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Button from '@/components/ui/Button';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';

interface TripInfo {
  id: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
}

interface TripControlsProps {
  trip: TripInfo;
  onStartTrip: () => void;
  onEndTrip: () => void;
  onReportIncident: () => void;
  onContactControl: () => void;
}

export function TripControls({
  trip,
  onStartTrip,
  onEndTrip,
  onReportIncident,
  onContactControl,
}: TripControlsProps) {
  const { colors } = useColorScheme();
  const isInProgress = trip.status === 'in-progress';

  return (
    <View style={[styles.container, { backgroundColor: colors.card }]}>
      <View style={styles.mainButtons}>
        {trip.status === 'scheduled' ? (
          <Button
            title="Start Trip"
            variant="primary"
            size="lg"
            icon={<Play size={20} color={Colors.white} />}
            onPress={onStartTrip}
            fullWidth
          />
        ) : trip.status === 'in-progress' ? (
          <Button
            title="End Trip"
            variant="primary"
            size="lg"
            icon={<Square size={20} color={Colors.white} />}
            onPress={onEndTrip}
            fullWidth
          />
        ) : (
          <Button
            title={trip.status === 'completed' ? 'Trip Completed' : 'Trip Cancelled'}
            variant="outline"
            size="lg"
            disabled
            fullWidth
          />
        )}
      </View>

      <View style={styles.actionRow}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: Colors.warning[500] + '15' },
          ]}
          onPress={onReportIncident}
          disabled={trip.status === 'completed' || trip.status === 'cancelled'}
        >
          <AlertTriangle size={24} color={Colors.warning[500]} />
          <Text variant="caption" weight="medium" color={Colors.warning[500]}>
            Report Incident
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            { backgroundColor: Colors.primary[500] + '15' },
          ]}
          onPress={onContactControl}
        >
          <MessageCircle size={24} color={Colors.primary[500]} />
          <Text variant="caption" weight="medium" color={Colors.primary[500]}>
            Contact Control
          </Text>
        </TouchableOpacity>
      </View>

      {isInProgress && (
        <View style={styles.statusContainer}>
          <View
            style={[styles.statusDot, { backgroundColor: Colors.success[500] }]}
          />
          <Text variant="bodySmall" weight="medium">
            Sharing location with control center
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingVertical: Spacing.md,
    paddingHorizontal: Spacing.md,
    borderTopWidth: 1,
    borderColor: Colors.gray[200],
    gap: Spacing.md,
  },
  mainButtons: {
    marginBottom: Spacing.sm,
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: Spacing.sm,
  },
  actionButton: {
    flex: 1,
    padding: Spacing.md,
    borderRadius: Layout.borderRadius.md,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing.sm,
    gap: Spacing.xs,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});

export default TripControls;