import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import Text from '@/components/ui/Text';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';

export interface Message {
  id: string;
  text: string;
  sender: string;
  timestamp: string;
  isFromDriver: boolean;
  isRead: boolean;
  priority?: 'normal' | 'high' | 'urgent';
}

interface MessageItemProps {
  message: Message;
}

export function MessageItem({ message }: MessageItemProps) {
  const { colors } = useColorScheme();
  
  const getPriorityColor = (priority: Message['priority']) => {
    switch (priority) {
      case 'high':
        return Colors.warning[500];
      case 'urgent':
        return Colors.error[500];
      default:
        return null;
    }
  };
  
  const priorityColor = message.priority ? getPriorityColor(message.priority) : null;
  const isFromDriver = message.isFromDriver;
  
  return (
    <View style={[
      styles.container,
      isFromDriver ? styles.outgoing : styles.incoming,
      { 
        backgroundColor: isFromDriver ? 
          Colors.primary[500] : 
          (priorityColor ? priorityColor + '15' : colors.card),
        borderColor: isFromDriver ? 'transparent' : colors.border,
      }
    ]}>
      {!isFromDriver && message.priority && message.priority !== 'normal' && (
        <View style={[
          styles.priorityBadge,
          { backgroundColor: priorityColor }
        ]}>
          <Text variant="caption" weight="bold" color={Colors.white}>
            {message.priority.toUpperCase()}
          </Text>
        </View>
      )}
      
      <Text 
        variant="bodySmall" 
        weight={message.priority === 'urgent' ? 'semibold' : 'regular'}
        color={isFromDriver ? Colors.white : (priorityColor || colors.text)}
      >
        {message.text}
      </Text>
      
      <View style={styles.footer}>
        <Text 
          variant="caption" 
          color={isFromDriver ? Colors.white + 'CC' : Colors.gray[500]}
        >
          {message.timestamp}
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: Spacing.md,
    borderRadius: Layout.borderRadius.md,
    maxWidth: '80%',
    marginVertical: Spacing.xs,
    borderWidth: 1,
    position: 'relative',
  },
  incoming: {
    alignSelf: 'flex-start',
    borderBottomLeftRadius: 4,
  },
  outgoing: {
    alignSelf: 'flex-end',
    borderBottomRightRadius: 4,
  },
  priorityBadge: {
    position: 'absolute',
    top: -10,
    right: 10,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  footer: {
    marginTop: Spacing.xs,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
});

export default MessageItem;