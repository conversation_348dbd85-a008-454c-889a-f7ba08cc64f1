import React from 'react';
import { Text as RNText, StyleSheet, TextStyle, TextProps as RNTextProps } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';

type TextVariant = 'h1' | 'h2' | 'h3' | 'h4' | 'body' | 'bodySmall' | 'caption' | 'button';
type TextWeight = 'regular' | 'medium' | 'semibold' | 'bold';
type TextAlignment = 'auto' | 'left' | 'right' | 'center' | 'justify';

interface TextProps extends RNTextProps {
  variant?: TextVariant;
  weight?: TextWeight;
  color?: string;
  align?: TextAlignment;
  style?: TextStyle;
  children: React.ReactNode;
}

export function Text({ 
  variant = 'body', 
  weight = 'regular', 
  color,
  align = 'left',
  style, 
  children,
  ...props 
}: TextProps) {
  const { colors } = useColorScheme();
  
  const textColor = color || colors.text;
  
  const variantStyles = styles[variant];
  
  const fontFamily = {
    regular: 'Inter-Regular',
    medium: 'Inter-Medium',
    semibold: 'Inter-SemiBold',
    bold: 'Inter-Bold',
  }[weight];
  
  return (
    <RNText 
      style={[
        variantStyles, 
        { 
          color: textColor,
          fontFamily,
          textAlign: align,
        },
        style,
      ]}
      {...props}
    >
      {children}
    </RNText>
  );
}

const styles = StyleSheet.create({
  h1: {
    fontSize: 28,
    lineHeight: 34,
  },
  h2: {
    fontSize: 24,
    lineHeight: 30,
  },
  h3: {
    fontSize: 20,
    lineHeight: 26,
  },
  h4: {
    fontSize: 18,
    lineHeight: 24,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
  },
  bodySmall: {
    fontSize: 14,
    lineHeight: 21,
  },
  caption: {
    fontSize: 12,
    lineHeight: 18,
  },
  button: {
    fontSize: 16,
    lineHeight: 24,
  },
});

export default Text;