import React from 'react';
import { View, StyleSheet, ViewStyle } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import Layout from '@/constants/Layout';
import Spacing from '@/constants/Spacing';

interface CardProps {
  children: React.ReactNode;
  style?: ViewStyle;
  variant?: 'default' | 'outlined' | 'elevated';
  padding?: keyof typeof Spacing | number;
}

export function Card({ 
  children, 
  style, 
  variant = 'default', 
  padding = 'md',
}: CardProps) {
  const { colors } = useColorScheme();

  const cardStyle: ViewStyle = {
    backgroundColor: colors.card,
    borderRadius: Layout.borderRadius.md,
    padding: typeof padding === 'string' ? Spacing[padding] : padding,
    ...(variant === 'outlined' && {
      borderWidth: 1,
      borderColor: colors.border,
    }),
    ...(variant === 'elevated' && {
      shadowColor: colors.shadow,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 3,
    }),
  };

  return (
    <View style={[styles.card, cardStyle, style]}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    overflow: 'hidden',
  },
});

export default Card;