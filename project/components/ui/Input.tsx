import React from 'react';
import { View, TextInput, StyleSheet, TextInputProps } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import Text from './Text';

interface InputProps extends TextInputProps {
  label?: string;
  error?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export function Input({ 
  label, 
  error, 
  leftIcon, 
  rightIcon, 
  style, 
  ...props 
}: InputProps) {
  const { colors } = useColorScheme();
  
  const inputStyles = [
    styles.input,
    {
      color: colors.text,
      borderColor: error ? Colors.error[500] : colors.border,
      paddingLeft: leftIcon ? 40 : Spacing.md,
      paddingRight: rightIcon ? 40 : Spacing.md,
    },
    style
  ];
  
  return (
    <View style={styles.container}>
      {label && (
        <Text variant="bodySmall\" weight="medium\" style={styles.label}>
          {label}
        </Text>
      )}
      
      <View style={styles.inputContainer}>
        {leftIcon && (
          <View style={styles.leftIcon}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          style={inputStyles}
          placeholderTextColor={Colors.gray[400]}
          {...props}
        />
        
        {rightIcon && (
          <View style={styles.rightIcon}>
            {rightIcon}
          </View>
        )}
      </View>
      
      {error && (
        <Text variant="bodySmall" color={Colors.error[500]} style={styles.error}>
          {error}
        </Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },
  label: {
    marginBottom: Spacing.xs,
  },
  inputContainer: {
    position: 'relative',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    paddingVertical: Spacing.sm,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  leftIcon: {
    position: 'absolute',
    left: Spacing.md,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    zIndex: 1,
  },
  rightIcon: {
    position: 'absolute',
    right: Spacing.md,
    top: 0,
    bottom: 0,
    justifyContent: 'center',
    zIndex: 1,
  },
  error: {
    marginTop: Spacing.xs,
  }
});

export default Input;