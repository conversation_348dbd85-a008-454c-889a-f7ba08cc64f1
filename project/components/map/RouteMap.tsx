import React from 'react';
import { View, StyleSheet, Platform } from 'react-native';
import { Bus } from 'lucide-react-native';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';

interface Coordinate {
  latitude: number;
  longitude: number;
}

interface RouteMapProps {
  currentLocation?: Coordinate;
  route?: Coordinate[];
  stops?: Coordinate[];
  height?: number;
  showControls?: boolean;
}

export function RouteMap({
  currentLocation = { latitude: 9.0222, longitude: 38.7468 }, // Default: Addis Ababa
  route = [],
  stops = [],
  height = 300,
  showControls = true,
}: RouteMapProps) {
  // For web, we'll use an iframe with Google Maps embed
  if (Platform.OS === 'web') {
    const center = `${currentLocation.latitude},${currentLocation.longitude}`;
    const zoom = 14;
    const mapUrl = `https://www.google.com/maps/embed/v1/view?key=YOUR_GOOGLE_MAPS_API_KEY&center=${center}&zoom=${zoom}`;
    
    return (
      <View style={[styles.container, { height }]}>
        <iframe
          src={mapUrl}
          style={{ border: 0, width: '100%', height: '100%', borderRadius: 12 }}
          allowFullScreen
          loading="lazy"
          referrerPolicy="no-referrer-when-downgrade"
        />
      </View>
    );
  }

  // For native platforms, return a placeholder for now
  return (
    <View style={[styles.container, { height, backgroundColor: Colors.gray[200] }]}>
      <View style={styles.placeholderContent}>
        <Bus size={48} color={Colors.gray[500]} />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
    marginVertical: Spacing.md,
  },
  placeholderContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  }
});

export default RouteMap;