export const Colors = {
  primary: {
    50: '#E6F0FA',
    100: '#CCE0F5',
    200: '#99C2EB',
    300: '#66A3E0',
    400: '#3385D6',
    500: '#0057B8',
    600: '#004693',
    700: '#00346F',
    800: '#00234A',
    900: '#001125',
  },
  secondary: {
    50: '#E9F7EF',
    100: '#D4F0DF',
    200: '#A9E1BF',
    300: '#7ED29F',
    400: '#53C380',
    500: '#27AE60',
    600: '#1F8B4D',
    700: '#17683A',
    800: '#0F4526',
    900: '#082313',
  },
  accent: {
    50: '#FEF5E7',
    100: '#FDEBCF',
    200: '#FBD69F',
    300: '#F9C26F',
    400: '#F7AD3F',
    500: '#F39C12',
    600: '#C27D0E',
    700: '#915E0B',
    800: '#613F07',
    900: '#301F04',
  },
  success: {
    50: '#E8F7ED',
    100: '#D1EFDB',
    200: '#A3DFB7',
    300: '#75CF93',
    400: '#47BF6F',
    500: '#2E9D57',
    600: '#257E46',
    700: '#1C5E34',
    800: '#133F23',
    900: '#091F11',
  },
  warning: {
    50: '#FEF4E6',
    100: '#FDE9CC',
    200: '#FBD399',
    300: '#F9BE66',
    400: '#F7A833',
    500: '#F59300',
    600: '#C47600',
    700: '#935800',
    800: '#623B00',
    900: '#311D00',
  },
  error: {
    50: '#FDEDED',
    100: '#FBDBDB',
    200: '#F7B7B7',
    300: '#F39292',
    400: '#EF6E6E',
    500: '#EB4949',
    600: '#BC3A3A',
    700: '#8D2C2C',
    800: '#5E1D1D',
    900: '#2F0E0E',
  },
  gray: {
    50: '#F8F9FA',
    100: '#E9ECEF',
    200: '#DEE2E6',
    300: '#CED4DA',
    400: '#ADB5BD',
    500: '#6C757D',
    600: '#495057',
    700: '#343A40',
    800: '#212529',
    900: '#0D1117',
  },
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
};

export const ThemeColors = {
  light: {
    background: Colors.white,
    card: Colors.white,
    text: Colors.gray[800],
    border: Colors.gray[200],
    notification: Colors.primary[500],
    shadow: 'rgba(0, 0, 0, 0.1)',
    success: Colors.success[500],
    warning: Colors.warning[500],
    error: Colors.error[500],
    statusBar: 'dark',
  },
  dark: {
    background: Colors.gray[900],
    card: Colors.gray[800],
    text: Colors.gray[100],
    border: Colors.gray[700],
    notification: Colors.primary[400],
    shadow: 'rgba(0, 0, 0, 0.3)',
    success: Colors.success[400],
    warning: Colors.warning[400],
    error: Colors.error[400],
    statusBar: 'light',
  },
};

export default Colors;