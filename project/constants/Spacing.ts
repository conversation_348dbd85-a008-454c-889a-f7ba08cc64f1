// Base spacing unit (8px)
const BASE = 8;

export const Spacing = {
  xs: BASE / 2, // 4px
  sm: BASE, // 8px
  md: BASE * 2, // 16px
  lg: BASE * 3, // 24px
  xl: BASE * 4, // 32px
  xxl: BASE * 6, // 48px
  xxxl: BASE * 8, // 64px
  
  // Special cases
  hairline: 1,
  tiny: 2,
  
  // Helpers for common spacing patterns
  screenHorizontal: BASE * 2, // 16px
  screenVertical: BASE * 2, // 16px
  sectionVertical: BASE * 4, // 32px
  itemVertical: BASE * 2, // 16px
  itemHorizontal: BASE * 2, // 16px
};

export default Spacing;