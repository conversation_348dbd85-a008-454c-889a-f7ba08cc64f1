import React, { useState } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { TriangleAlert as AlertTriangle, ChevronRight, Filter, Plus } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

interface Incident {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  type: 'delay' | 'traffic' | 'mechanical' | 'road' | 'weather' | 'other';
  status: 'open' | 'in-progress' | 'resolved';
  severity: 'low' | 'medium' | 'high';
  route?: string;
}

const INCIDENTS_DATA: Incident[] = [
  {
    id: '1',
    title: 'Road Construction Delay',
    description: 'Construction on Main Street causing delays of approximately 15 minutes.',
    date: '2023-05-12',
    time: '09:35 AM',
    type: 'delay',
    status: 'resolved',
    severity: 'medium',
    route: 'R23',
  },
  {
    id: '2',
    title: 'Heavy Traffic Near University',
    description: 'Heavy congestion near the university entrance. Estimated delay of 20 minutes.',
    date: '2023-05-15',
    time: '08:15 AM',
    type: 'traffic',
    status: 'in-progress',
    severity: 'high',
    route: 'R24',
  },
  {
    id: '3',
    title: 'Flat Tire',
    description: 'Bus has a flat tire. Need replacement vehicle.',
    date: '2023-05-16',
    time: '10:22 AM',
    type: 'mechanical',
    status: 'open',
    severity: 'high',
    route: 'R25',
  },
  {
    id: '4',
    title: 'Road Closed Due to Accident',
    description: 'Road closed near downtown. Police directing traffic to alternate routes.',
    date: '2023-05-18',
    time: '03:45 PM',
    type: 'road',
    status: 'in-progress',
    severity: 'high',
    route: 'R26',
  },
  {
    id: '5',
    title: 'Weather Delay',
    description: 'Heavy rain causing reduced visibility and slower driving conditions.',
    date: '2023-05-20',
    time: '07:30 AM',
    type: 'weather',
    status: 'resolved',
    severity: 'medium',
    route: 'R27',
  },
];

export default function IncidentsScreen() {
  const router = useRouter();
  const { colors } = useColorScheme();
  const [incidents, setIncidents] = useState<Incident[]>(INCIDENTS_DATA);
  const [statusFilter, setStatusFilter] = useState<Incident['status'] | 'all'>('all');
  
  const filteredIncidents = statusFilter === 'all' 
    ? incidents 
    : incidents.filter(incident => incident.status === statusFilter);
  
  const getSeverityColor = (severity: Incident['severity']) => {
    switch (severity) {
      case 'high':
        return Colors.error[500];
      case 'medium':
        return Colors.warning[500];
      case 'low':
        return Colors.success[500];
      default:
        return Colors.gray[500];
    }
  };
  
  const getStatusColor = (status: Incident['status']) => {
    switch (status) {
      case 'open':
        return Colors.error[500];
      case 'in-progress':
        return Colors.warning[500];
      case 'resolved':
        return Colors.success[500];
      default:
        return Colors.gray[500];
    }
  };
  
  const getIncidentTypeIcon = (type: Incident['type']) => {
    // For simplicity, just using AlertTriangle for all types
    return <AlertTriangle size={20} color={Colors.gray[500]} />;
  };
  
  const handleReportIncident = () => {
    router.push("/(app)/report-incident");
  };
  
  const handleIncidentPress = (incident: Incident) => {
    // In a real app, this would navigate to incident details
    console.log('Incident pressed:', incident.id);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text variant="h3" weight="bold">
          Incidents
        </Text>
        
        <Button
          title="Report New"
          variant="primary"
          icon={<Plus size={18} color={Colors.white} />}
          onPress={handleReportIncident}
        />
      </View>
      
      <View style={styles.filtersContainer}>
        <View style={styles.filterLabel}>
          <Filter size={16} color={Colors.gray[500]} />
          <Text variant="bodySmall" color={Colors.gray[500]}>
            Filter by status:
          </Text>
        </View>
        
        <View style={styles.filterButtons}>
          {(['all', 'open', 'in-progress', 'resolved'] as const).map((status) => (
            <TouchableOpacity
              key={status}
              style={[
                styles.filterButton,
                statusFilter === status && styles.activeFilterButton,
                statusFilter === status && { 
                  backgroundColor: status === 'all' 
                    ? Colors.primary[500] 
                    : getStatusColor(status === 'all' ? 'open' : status) + '20'
                }
              ]}
              onPress={() => setStatusFilter(status)}
            >
              <Text
                variant="caption"
                weight="medium"
                color={statusFilter === status ? 
                  (status === 'all' ? Colors.white : getStatusColor(status === 'all' ? 'open' : status)) 
                  : Colors.gray[500]}
              >
                {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
      
      <FlatList
        data={filteredIncidents}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
        renderItem={({ item }) => (
          <TouchableOpacity 
            onPress={() => handleIncidentPress(item)}
            style={styles.incidentCard}
            activeOpacity={0.7}
          >
            <Card variant="elevated">
              <View style={styles.incidentHeader}>
                <View style={styles.incidentTitleContainer}>
                  <View style={[
                    styles.severityIndicator, 
                    { backgroundColor: getSeverityColor(item.severity) }
                  ]} />
                  <Text variant="body" weight="semibold" numberOfLines={1}>
                    {item.title}
                  </Text>
                </View>
                
                {item.route && (
                  <View style={styles.routeBadge}>
                    <Text variant="caption" weight="bold" color={Colors.white}>
                      {item.route}
                    </Text>
                  </View>
                )}
              </View>
              
              <Text 
                variant="bodySmall" 
                color={Colors.gray[600]} 
                numberOfLines={2} 
                style={styles.description}
              >
                {item.description}
              </Text>
              
              <View style={styles.incidentFooter}>
                <View style={styles.incidentMeta}>
                  <View style={styles.metaItem}>
                    {getIncidentTypeIcon(item.type)}
                    <Text variant="caption" color={Colors.gray[500]} style={styles.metaText}>
                      {item.type.charAt(0).toUpperCase() + item.type.slice(1)}
                    </Text>
                  </View>
                  
                  <Text variant="caption" color={Colors.gray[500]}>
                    {item.time} • {item.date}
                  </Text>
                </View>
                
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(item.status) + '20' }
                ]}>
                  <Text 
                    variant="caption" 
                    weight="medium" 
                    color={getStatusColor(item.status)}
                  >
                    {item.status.charAt(0).toUpperCase() + item.status.slice(1).replace('-', ' ')}
                  </Text>
                </View>
                
                <ChevronRight size={16} color={Colors.gray[400]} />
              </View>
            </Card>
          </TouchableOpacity>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text variant="body" color={Colors.gray[500]} align="center">
              No incidents matching the selected filter.
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: Spacing.md,
    paddingTop: Spacing.lg,
  },
  filtersContainer: {
    paddingHorizontal: Spacing.md,
    marginBottom: Spacing.md,
  },
  filterLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.xs,
    gap: 4,
  },
  filterButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: Spacing.sm,
  },
  filterButton: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: Layout.borderRadius.sm,
    backgroundColor: Colors.gray[200],
  },
  activeFilterButton: {
    backgroundColor: Colors.primary[500],
  },
  listContent: {
    padding: Spacing.md,
    paddingTop: 0,
  },
  incidentCard: {
    marginBottom: Spacing.md,
  },
  incidentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.xs,
  },
  incidentTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: Spacing.sm,
  },
  severityIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: Spacing.sm,
  },
  routeBadge: {
    backgroundColor: Colors.primary[500],
    paddingVertical: 2,
    paddingHorizontal: 6,
    borderRadius: Layout.borderRadius.sm,
  },
  description: {
    marginBottom: Spacing.md,
  },
  incidentFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  incidentMeta: {
    flex: 1,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  metaText: {
    marginLeft: 4,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: Layout.borderRadius.sm,
    marginRight: Spacing.sm,
  },
  emptyContainer: {
    marginTop: Spacing.xxxl,
    alignItems: 'center',
  },
});