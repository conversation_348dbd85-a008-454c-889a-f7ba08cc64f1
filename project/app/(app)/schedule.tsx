import React, { useState } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { CalendarDays, ChevronLeft, ChevronRight } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import ScheduleCard, { ScheduleItem } from '@/components/schedule/ScheduleCard';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample schedule data
const SCHEDULE_DATA: ScheduleItem[] = [
  {
    id: '1',
    routeNumber: 'R23',
    routeName: 'Central Station - University',
    startLocation: 'Central Bus Station',
    endLocation: 'University Campus',
    startTime: '08:00 AM',
    endTime: '09:30 AM',
    status: 'scheduled',
    passengerCount: 42,
  },
  {
    id: '2',
    routeNumber: 'R24',
    routeName: 'University - Shopping Mall',
    startLocation: 'University Campus',
    endLocation: 'City Mall',
    startTime: '10:15 AM',
    endTime: '11:30 AM',
    status: 'scheduled',
    passengerCount: 35,
  },
  {
    id: '3',
    routeNumber: 'R25',
    routeName: 'Shopping Mall - Central Station',
    startLocation: 'City Mall',
    endLocation: 'Central Bus Station',
    startTime: '12:45 PM',
    endTime: '02:00 PM',
    status: 'scheduled',
    hasWarning: true,
  },
  {
    id: '4',
    routeNumber: 'R26',
    routeName: 'Central Station - Hospital',
    startLocation: 'Central Bus Station',
    endLocation: 'Main Hospital',
    startTime: '03:15 PM',
    endTime: '04:15 PM',
    status: 'scheduled',
  },
  {
    id: '5',
    routeNumber: 'R27',
    routeName: 'Hospital - University',
    startLocation: 'Main Hospital',
    endLocation: 'University Campus',
    startTime: '04:45 PM',
    endTime: '06:00 PM',
    status: 'scheduled',
    passengerCount: 28,
  },
];

export default function ScheduleScreen() {
  const router = useRouter();
  const { colors } = useColorScheme();
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDay, setSelectedDay] = useState(0); // 0 = today
  
  // Generate days for the week view
  const getDays = () => {
    const days = [];
    const today = new Date();
    
    for (let i = -1; i < 6; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      days.push({
        date,
        dayName: date.toLocaleDateString([], { weekday: 'short' }),
        dayNumber: date.getDate(),
        isToday: i === 0,
      });
    }
    
    return days;
  };
  
  const days = getDays();
  
  const handlePreviousDay = () => {
    if (selectedDay > -1) {
      setSelectedDay(selectedDay - 1);
    }
  };
  
  const handleNextDay = () => {
    if (selectedDay < 5) {
      setSelectedDay(selectedDay + 1);
    }
  };
  
  const handleSchedulePress = (item: ScheduleItem) => {
    router.push({
      pathname: "/(app)/trip-details",
      params: { id: item.id }
    });
  };
  
  const formatDisplayDate = (date: Date): string => {
    return date.toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric' });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.card }]}>
        <Text variant="h3" weight="bold" style={styles.headerTitle}>
          Schedule
        </Text>
        
        <View style={styles.dateHeader}>
          <View style={styles.calendarIcon}>
            <CalendarDays size={20} color={Colors.primary[500]} />
          </View>
          <Text variant="body" weight="semibold">
            {formatDisplayDate(days[selectedDay + 1]?.date || new Date())}
          </Text>
        </View>
        
        <View style={styles.daySelector}>
          <TouchableOpacity 
            onPress={handlePreviousDay}
            disabled={selectedDay <= -1}
            style={[styles.dayNavButton, selectedDay <= -1 && styles.dayNavButtonDisabled]}
          >
            <ChevronLeft size={20} color={selectedDay <= -1 ? Colors.gray[400] : Colors.gray[700]} />
          </TouchableOpacity>
          
          <FlatList
            horizontal
            data={days}
            keyExtractor={(item, index) => `day-${index}`}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.daysContainer}
            renderItem={({ item, index }) => (
              <TouchableOpacity
                style={[
                  styles.dayItem,
                  selectedDay + 1 === index && styles.selectedDayItem,
                  { borderColor: colors.border }
                ]}
                onPress={() => setSelectedDay(index - 1)}
              >
                <Text 
                  variant="caption" 
                  weight="medium"
                  color={selectedDay + 1 === index ? Colors.primary[500] : Colors.gray[500]}
                >
                  {item.dayName}
                </Text>
                <Text 
                  variant="body" 
                  weight={selectedDay + 1 === index ? 'bold' : 'semibold'}
                  color={selectedDay + 1 === index ? Colors.primary[500] : colors.text}
                >
                  {item.dayNumber}
                </Text>
                {item.isToday && (
                  <View 
                    style={[
                      styles.todayIndicator, 
                      { backgroundColor: selectedDay + 1 === index ? Colors.primary[500] : Colors.accent[500] }
                    ]} 
                  />
                )}
              </TouchableOpacity>
            )}
          />
          
          <TouchableOpacity 
            onPress={handleNextDay}
            disabled={selectedDay >= 5}
            style={[styles.dayNavButton, selectedDay >= 5 && styles.dayNavButtonDisabled]}
          >
            <ChevronRight size={20} color={selectedDay >= 5 ? Colors.gray[400] : Colors.gray[700]} />
          </TouchableOpacity>
        </View>
      </View>
      
      <FlatList
        data={SCHEDULE_DATA}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <ScheduleCard 
            item={item}
            onPress={handleSchedulePress}
          />
        )}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text variant="body" color={Colors.gray[500]} align="center">
              No trips scheduled for this day.
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: Spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray[200],
  },
  headerTitle: {
    marginBottom: Spacing.md,
  },
  dateHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  calendarIcon: {
    marginRight: Spacing.sm,
  },
  daySelector: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dayNavButton: {
    padding: Spacing.sm,
  },
  dayNavButtonDisabled: {
    opacity: 0.5,
  },
  daysContainer: {
    paddingVertical: Spacing.sm,
  },
  dayItem: {
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: Spacing.sm,
    width: 45,
    height: 65,
    borderWidth: 1,
    borderRadius: 8,
    paddingVertical: Spacing.sm,
    position: 'relative',
  },
  selectedDayItem: {
    borderColor: Colors.primary[500],
    borderWidth: 2,
  },
  todayIndicator: {
    position: 'absolute',
    bottom: 4,
    width: 4,
    height: 4,
    borderRadius: 2,
  },
  listContent: {
    padding: Spacing.md,
  },
  emptyContainer: {
    marginTop: Spacing.xxxl,
    alignItems: 'center',
  },
});