import React, { useState, useRef } from 'react';
import { View, StyleSheet, FlatList, KeyboardAvoidingView, Platform, TextInput, TouchableOpacity } from 'react-native';
import { Send, TriangleAlert as AlertTriangle } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import MessageItem, { Message } from '@/components/messages/MessageItem';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample messages data
const MESSAGES_DATA: Message[] = [
  {
    id: '1',
    text: 'Good morning! Your route today includes a detour due to road construction near the university.',
    sender: 'Control Center',
    timestamp: '08:15 AM',
    isFromDriver: false,
    isRead: true,
    priority: 'high',
  },
  {
    id: '2',
    text: 'I understand. What\'s the recommended detour route?',
    sender: 'Driver',
    timestamp: '08:18 AM',
    isFromDriver: true,
    isRead: true,
  },
  {
    id: '3',
    text: 'Please take Main St. to 5th Avenue, then continue on your regular route from there. This will add approximately 10 minutes to your journey.',
    sender: 'Control Center',
    timestamp: '08:20 AM',
    isFromDriver: false,
    isRead: true,
  },
  {
    id: '4',
    text: 'Got it. I\'ll follow the detour via Main St. and 5th Avenue.',
    sender: 'Driver',
    timestamp: '08:22 AM',
    isFromDriver: true,
    isRead: true,
  },
  {
    id: '5',
    text: 'ALERT: Heavy traffic reported ahead on your route. Expected delay of 15-20 minutes. Consider alternate route via Westside Blvd.',
    sender: 'Control Center',
    timestamp: '09:05 AM',
    isFromDriver: false,
    isRead: true,
    priority: 'urgent',
  },
];

export default function MessagesScreen() {
  const { colors } = useColorScheme();
  const [messages, setMessages] = useState<Message[]>(MESSAGES_DATA);
  const [newMessage, setNewMessage] = useState('');
  const flatListRef = useRef<FlatList>(null);
  
  const sendMessage = () => {
    if (newMessage.trim() === '') return;
    
    const newMsg: Message = {
      id: (messages.length + 1).toString(),
      text: newMessage,
      sender: 'Driver',
      timestamp: new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      isFromDriver: true,
      isRead: true,
    };
    
    setMessages([...messages, newMsg]);
    setNewMessage('');
    
    // Scroll to bottom on next render
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <View style={styles.header}>
        <Text variant="h3" weight="bold">
          Control Center Chat
        </Text>
      </View>
      
      <View style={styles.alertBanner}>
        <AlertTriangle size={20} color={Colors.white} />
        <Text variant="bodySmall" weight="semibold" color={Colors.white} style={styles.alertText}>
          This channel is monitored 24/7. For emergencies, call dispatch directly.
        </Text>
      </View>

      <View style={styles.messagesContainer}>
        <FlatList
          ref={flatListRef}
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => <MessageItem message={item} />}
          contentContainerStyle={styles.messagesList}
          initialNumToRender={10}
          onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
        />
      </View>
      
      <View style={[styles.inputContainer, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <TextInput
          style={[
            styles.input,
            { color: colors.text, backgroundColor: colors.background, borderColor: colors.border }
          ]}
          value={newMessage}
          onChangeText={setNewMessage}
          placeholder="Type a message..."
          placeholderTextColor={Colors.gray[400]}
          multiline
          maxLength={500}
        />
        <TouchableOpacity
          onPress={sendMessage}
          style={[
            styles.sendButton,
            {
              backgroundColor: newMessage.trim() ? Colors.primary[500] : Colors.gray[300],
            },
          ]}
          disabled={!newMessage.trim()}
        >
          <Send size={20} color={Colors.white} />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: Spacing.md,
    paddingTop: Spacing.lg,
  },
  alertBanner: {
    backgroundColor: Colors.error[500],
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    marginHorizontal: Spacing.md,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Spacing.md,
  },
  alertText: {
    marginLeft: Spacing.xs,
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesList: {
    padding: Spacing.md,
    paddingBottom: Spacing.lg,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: Spacing.sm,
    borderTopWidth: 1,
  },
  input: {
    flex: 1,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.md,
    padding: Spacing.sm,
    maxHeight: 100,
    fontSize: 16,
  },
  sendButton: {
    marginLeft: Spacing.sm,
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
});