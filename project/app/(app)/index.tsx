import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, RefreshControl } from 'react-native';
import { useRouter } from 'expo-router';
import { ArrowRight, Clock, MapPin, Calendar } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import RouteMap from '@/components/map/RouteMap';
import ScheduleCard, { ScheduleItem } from '@/components/schedule/ScheduleCard';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample data
const TODAY_ROUTE = {
  id: '1',
  routeNumber: 'R23',
  routeName: 'Central Station - University',
  startLocation: 'Central Bus Station',
  endLocation: 'University Campus',
  startTime: '08:00 AM',
  endTime: '09:30 AM',
  status: 'scheduled',
  passengerCount: 42,
  hasWarning: false,
};

const ROUTE_COORDS = [
  { latitude: 9.0222, longitude: 38.7468 }, // Start point
  { latitude: 9.0295, longitude: 38.7577 },
  { latitude: 9.0350, longitude: 38.7650 },
  { latitude: 9.0400, longitude: 38.7630 }, // End point
];

const STOPS = [
  { latitude: 9.0222, longitude: 38.7468 }, // Start
  { latitude: 9.0295, longitude: 38.7577 }, // Mid stop 1
  { latitude: 9.0350, longitude: 38.7650 }, // Mid stop 2
  { latitude: 9.0400, longitude: 38.7630 }, // End
];

export default function HomeScreen() {
  const router = useRouter();
  const { colors } = useColorScheme();
  const [refreshing, setRefreshing] = useState(false);
  const [currentLocation, setCurrentLocation] = useState({ latitude: 9.0222, longitude: 38.7468 });
  const [timeNow, setTimeNow] = useState('');
  const [dateToday, setDateToday] = useState('');
  
  // Update time
  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      setTimeNow(now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
      setDateToday(now.toLocaleDateString([], { weekday: 'long', month: 'long', day: 'numeric' }));
    };
    
    updateDateTime();
    const timer = setInterval(updateDateTime, 60000); // Update every minute
    
    return () => clearInterval(timer);
  }, []);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    
    // Simulate a refresh
    setTimeout(() => {
      setRefreshing(false);
    }, 1500);
  }, []);
  
  const handleStartTrip = () => {
    router.push({
      pathname: "/(app)/trip-details",
      params: { id: TODAY_ROUTE.id }
    });
  };
  
  const handleSchedulePress = (item: ScheduleItem) => {
    router.push({
      pathname: "/(app)/trip-details",
      params: { id: item.id }
    });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <View style={styles.headerContainer}>
          <View>
            <Text variant="h2" weight="bold">
              Welcome, Driver
            </Text>
            <View style={styles.dateTimeContainer}>
              <Clock size={16} color={Colors.gray[500]} />
              <Text 
                variant="bodySmall" 
                color={Colors.gray[500]}
                style={styles.dateTimeText}
              >
                {timeNow}
              </Text>
              <Calendar size={16} color={Colors.gray[500]} />
              <Text 
                variant="bodySmall" 
                color={Colors.gray[500]}
                style={styles.dateTimeText}
              >
                {dateToday}
              </Text>
            </View>
          </View>
        </View>

        <Card variant="elevated" style={styles.nextTripCard}>
          <View style={styles.nextTripHeader}>
            <Text variant="h3" weight="semibold">
              Next Trip
            </Text>
            <TouchableOpacity 
              onPress={() => router.push('/(app)/schedule')}
              style={styles.viewAllButton}
            >
              <Text variant="bodySmall" color={Colors.primary[500]}>
                View All
              </Text>
              <ArrowRight size={16} color={Colors.primary[500]} />
            </TouchableOpacity>
          </View>

          <View style={styles.tripDetailsContainer}>
            <View style={styles.routeNumberContainer}>
              <Text weight="bold" variant="h2" color={Colors.primary[500]}>
                {TODAY_ROUTE.routeNumber}
              </Text>
            </View>
            
            <View style={styles.tripInfoContainer}>
              <Text variant="h4" weight="semibold">
                {TODAY_ROUTE.routeName}
              </Text>
              
              <View style={styles.tripInfoRow}>
                <Clock size={18} color={Colors.primary[500]} />
                <Text variant="body" style={styles.tripInfoText}>
                  {TODAY_ROUTE.startTime} - {TODAY_ROUTE.endTime}
                </Text>
              </View>
              
              <View style={styles.locations}>
                <View style={styles.locationItem}>
                  <View style={[styles.locationDot, { backgroundColor: Colors.primary[500] }]} />
                  <Text variant="bodySmall">
                    {TODAY_ROUTE.startLocation}
                  </Text>
                </View>
                
                <View style={styles.locationLine} />
                
                <View style={styles.locationItem}>
                  <View style={[styles.locationDot, { backgroundColor: Colors.accent[500] }]} />
                  <Text variant="bodySmall">
                    {TODAY_ROUTE.endLocation}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <RouteMap
            currentLocation={currentLocation}
            route={ROUTE_COORDS}
            stops={STOPS}
            height={200}
            showControls={false}
          />

          <Button
            title="Start Trip"
            onPress={handleStartTrip}
            fullWidth
            size="md"
          />
        </Card>

        <View style={styles.sectionHeader}>
          <Text variant="h3" weight="semibold">
            Today's Schedule
          </Text>
          <TouchableOpacity 
            onPress={() => router.push('/(app)/schedule')}
            style={styles.viewAllButton}
          >
            <Text variant="bodySmall" color={Colors.primary[500]}>
              View All
            </Text>
            <ArrowRight size={16} color={Colors.primary[500]} />
          </TouchableOpacity>
        </View>

        <ScheduleCard 
          item={{
            ...TODAY_ROUTE,
            id: '1'
          }}
          onPress={handleSchedulePress}
        />
        
        <ScheduleCard 
          item={{
            id: '2',
            routeNumber: 'R24',
            routeName: 'University - Shopping Mall',
            startLocation: 'University Campus',
            endLocation: 'City Mall',
            startTime: '10:15 AM',
            endTime: '11:30 AM',
            status: 'scheduled',
            passengerCount: 35,
          }}
          onPress={handleSchedulePress}
        />
        
        <ScheduleCard 
          item={{
            id: '3',
            routeNumber: 'R25',
            routeName: 'Shopping Mall - Central Station',
            startLocation: 'City Mall',
            endLocation: 'Central Bus Station',
            startTime: '12:45 PM',
            endTime: '02:00 PM',
            status: 'scheduled',
            hasWarning: true,
          }}
          onPress={handleSchedulePress}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingTop: Spacing.xl,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  dateTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
  },
  dateTimeText: {
    marginLeft: 4,
    marginRight: Spacing.md,
  },
  nextTripCard: {
    marginBottom: Spacing.xl,
  },
  nextTripHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tripDetailsContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.md,
  },
  routeNumberContainer: {
    marginRight: Spacing.md,
    justifyContent: 'center',
  },
  tripInfoContainer: {
    flex: 1,
  },
  tripInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.xs,
    marginBottom: Spacing.sm,
  },
  tripInfoText: {
    marginLeft: Spacing.xs,
  },
  locations: {
    marginBottom: Spacing.sm,
  },
  locationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 4,
  },
  locationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    marginRight: 8,
  },
  locationLine: {
    width: 1,
    height: 16,
    backgroundColor: Colors.gray[300],
    marginLeft: 5,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
    marginTop: Spacing.lg,
  },
});