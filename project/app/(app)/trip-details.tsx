import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { ChevronLeft, Clock, Users, Route } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import RouteMap from '@/components/map/RouteMap';
import TripControls from '@/components/trips/TripControls';
import useLocation from '@/hooks/useLocation';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample data
const TRIP_DATA = {
  id: '1',
  routeNumber: 'R23',
  routeName: 'Central Station - University',
  startLocation: 'Central Bus Station',
  endLocation: 'University Campus',
  startTime: '08:00 AM',
  endTime: '09:30 AM',
  status: 'scheduled',
  stops: [
    { name: 'Central Bus Station', time: '08:00 AM', latitude: 9.0222, longitude: 38.7468 },
    { name: 'Market Square', time: '08:15 AM', latitude: 9.0295, longitude: 38.7577 },
    { name: 'City Park', time: '08:30 AM', latitude: 9.0350, longitude: 38.7650 },
    { name: 'University Campus', time: '09:30 AM', latitude: 9.0400, longitude: 38.7630 }
  ],
  distance: '7.5 km',
  passengerCapacity: 50,
  estimatedPassengers: 42,
  busNumber: 'BUS-2302',
  notes: 'Construction near City Park stop, expect minor delays.'
};

const ROUTE_COORDS = [
  { latitude: 9.0222, longitude: 38.7468 }, // Start point
  { latitude: 9.0295, longitude: 38.7577 },
  { latitude: 9.0350, longitude: 38.7650 },
  { latitude: 9.0400, longitude: 38.7630 }, // End point
];

const STOPS = [
  { latitude: 9.0222, longitude: 38.7468 }, // Start
  { latitude: 9.0295, longitude: 38.7577 }, // Mid stop 1
  { latitude: 9.0350, longitude: 38.7650 }, // Mid stop 2
  { latitude: 9.0400, longitude: 38.7630 }, // End
];

export default function TripDetails() {
  const { id } = useLocalSearchParams();
  const router = useRouter();
  const { colors } = useColorScheme();
  const { location, startTracking, stopTracking, isTracking, errorMsg } = useLocation();
  
  const [trip, setTrip] = useState({ ...TRIP_DATA });
  const [currentLocation, setCurrentLocation] = useState(null);
  
  useEffect(() => {
    if (location) {
      setCurrentLocation({
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      });
    }
  }, [location]);

  const handleStartTrip = () => {
    Alert.alert(
      "Start Trip",
      "Are you ready to start this trip? Your location will be shared with the control center.",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "Start Trip", 
          onPress: () => {
            setTrip(prev => ({ ...prev, status: 'in-progress' }));
            startTracking();
          }
        }
      ]
    );
  };
  
  const handleEndTrip = () => {
    Alert.alert(
      "End Trip",
      "Are you sure you want to end this trip?",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "End Trip", 
          onPress: () => {
            setTrip(prev => ({ ...prev, status: 'completed' }));
            stopTracking();
          }
        }
      ]
    );
  };
  
  const handleReportIncident = () => {
    router.push("/(app)/report-incident");
  };
  
  const handleContactControl = () => {
    router.push("/(app)/messages");
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen 
        options={{
          headerShown: true,
          headerTitle: "Trip Details",
          headerLeft: () => (
            <Button
              variant="text"
              onPress={() => router.back()}
              title=""
              icon={<ChevronLeft color={colors.text} size={24} />}
            />
          )
        }}
      />
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <Card variant="elevated" style={styles.routeCard}>
          <View style={styles.routeHeader}>
            <View style={styles.routeNumberBadge}>
              <Text weight="bold" color={Colors.white}>
                {trip.routeNumber}
              </Text>
            </View>
            <Text variant="h3" weight="semibold">
              {trip.routeName}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <View style={styles.infoItem}>
              <Clock size={16} color={Colors.gray[500]} />
              <Text variant="bodySmall" style={styles.infoText}>
                {trip.startTime} - {trip.endTime}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Route size={16} color={Colors.gray[500]} />
              <Text variant="bodySmall" style={styles.infoText}>
                {trip.distance}
              </Text>
            </View>
            <View style={styles.infoItem}>
              <Users size={16} color={Colors.gray[500]} />
              <Text variant="bodySmall" style={styles.infoText}>
                {trip.estimatedPassengers}/{trip.passengerCapacity}
              </Text>
            </View>
          </View>
          
          <View style={styles.divider} />
          
          <Text variant="body" weight="semibold">
            Bus: {trip.busNumber}
          </Text>
          
          {trip.notes && (
            <Card 
              variant="outlined" 
              style={[styles.notesCard, { borderColor: Colors.warning[300] }]}
              padding="sm"
            >
              <Text variant="bodySmall" color={Colors.warning[700]}>
                Note: {trip.notes}
              </Text>
            </Card>
          )}
        </Card>
        
        <Text variant="h4" weight="semibold" style={styles.sectionTitle}>
          Route Map
        </Text>
        
        <RouteMap
          currentLocation={currentLocation || ROUTE_COORDS[0]}
          route={ROUTE_COORDS}
          stops={STOPS}
          height={250}
        />
        
        <Text variant="h4" weight="semibold" style={styles.sectionTitle}>
          Stops
        </Text>
        
        <Card variant="elevated">
          {trip.stops.map((stop, index) => (
            <View key={index} style={styles.stopItem}>
              <View style={styles.stopTimeContainer}>
                <Text variant="body" weight="semibold">
                  {stop.time}
                </Text>
              </View>
              
              <View style={styles.stopDetailsContainer}>
                <View style={styles.stopNameContainer}>
                  <View style={[
                    styles.stopDot,
                    index === 0 
                      ? { backgroundColor: Colors.primary[500] } 
                      : index === trip.stops.length - 1 
                        ? { backgroundColor: Colors.accent[500] } 
                        : { backgroundColor: Colors.secondary[500] }
                  ]} />
                  <Text variant="body">
                    {stop.name}
                  </Text>
                </View>
                
                {index < trip.stops.length - 1 && (
                  <View style={styles.stopConnector} />
                )}
              </View>
            </View>
          ))}
        </Card>
      </ScrollView>
      
      <TripControls
        trip={trip}
        onStartTrip={handleStartTrip}
        onEndTrip={handleEndTrip}
        onReportIncident={handleReportIncident}
        onContactControl={handleContactControl}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingBottom: Spacing.xxl * 2,
  },
  routeCard: {
    marginBottom: Spacing.lg,
  },
  routeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  routeNumberBadge: {
    backgroundColor: Colors.primary[500],
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 6,
    marginRight: Spacing.sm,
  },
  infoRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.md,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: Spacing.lg,
    marginBottom: Spacing.xs,
  },
  infoText: {
    marginLeft: 4,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.gray[200],
    marginVertical: Spacing.md,
  },
  notesCard: {
    marginTop: Spacing.md,
  },
  sectionTitle: {
    marginTop: Spacing.xl,
    marginBottom: Spacing.md,
  },
  stopItem: {
    flexDirection: 'row',
    paddingVertical: Spacing.sm,
  },
  stopTimeContainer: {
    width: 80,
    paddingRight: Spacing.sm,
  },
  stopDetailsContainer: {
    flex: 1,
    position: 'relative',
  },
  stopNameContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  stopDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: Spacing.sm,
  },
  stopConnector: {
    position: 'absolute',
    left: 6,
    top: 16,
    bottom: -8,
    width: 1,
    backgroundColor: Colors.gray[300],
  },
});