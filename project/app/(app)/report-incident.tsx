import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { ChevronLeft, Camera, Upload, X } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

// Incident types
const INCIDENT_TYPES = [
  { id: 'delay', label: 'Delay' },
  { id: 'traffic', label: 'Traffic' },
  { id: 'mechanical', label: 'Mechanical Issue' },
  { id: 'road', label: 'Road Condition' },
  { id: 'weather', label: 'Weather' },
  { id: 'passenger', label: 'Passenger Issue' },
  { id: 'other', label: 'Other' },
];

// Incident severity levels
const SEVERITY_LEVELS = [
  { id: 'low', label: 'Low', color: Colors.success[500] },
  { id: 'medium', label: 'Medium', color: Colors.warning[500] },
  { id: 'high', label: 'High', color: Colors.error[500] },
];

export default function ReportIncidentScreen() {
  const router = useRouter();
  const { colors } = useColorScheme();
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [selectedSeverity, setSelectedSeverity] = useState('');
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(false);
  
  const isFormValid = title && description && selectedType && selectedSeverity;
  
  const handleAddPhoto = () => {
    // In a real app, this would use expo-camera or expo-image-picker
    // For this demo, we'll just simulate adding a photo
    Alert.alert(
      "Add Photo",
      "This function would normally access your camera or photo library.",
      [{ text: "OK" }]
    );
  };
  
  const handleSubmit = () => {
    if (!isFormValid) {
      Alert.alert(
        "Incomplete Form",
        "Please fill in all required fields before submitting.",
        [{ text: "OK" }]
      );
      return;
    }
    
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        "Incident Reported",
        "Your incident report has been submitted successfully.",
        [
          { 
            text: "OK", 
            onPress: () => router.back()
          }
        ]
      );
    }, 1500);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Stack.Screen 
        options={{
          headerShown: true,
          headerTitle: "Report Incident",
          headerLeft: () => (
            <Button
              variant="text"
              onPress={() => router.back()}
              title=""
              icon={<ChevronLeft color={colors.text} size={24} />}
            />
          )
        }}
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        keyboardShouldPersistTaps="handled"
      >
        <Input
          label="Incident Title"
          placeholder="Brief title describing the incident"
          value={title}
          onChangeText={setTitle}
        />
        
        <Text variant="bodySmall" weight="medium" style={styles.label}>
          Incident Type
        </Text>
        <View style={styles.typeContainer}>
          {INCIDENT_TYPES.map((type) => (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.typeButton,
                selectedType === type.id && {
                  backgroundColor: Colors.primary[500] + '20',
                  borderColor: Colors.primary[500],
                },
              ]}
              onPress={() => setSelectedType(type.id)}
            >
              <Text
                variant="bodySmall"
                color={selectedType === type.id ? Colors.primary[500] : Colors.gray[700]}
                weight={selectedType === type.id ? 'semibold' : 'regular'}
              >
                {type.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Text variant="bodySmall" weight="medium" style={styles.label}>
          Severity Level
        </Text>
        <View style={styles.severityContainer}>
          {SEVERITY_LEVELS.map((level) => (
            <TouchableOpacity
              key={level.id}
              style={[
                styles.severityButton,
                { borderColor: level.color },
                selectedSeverity === level.id && {
                  backgroundColor: level.color + '20',
                },
              ]}
              onPress={() => setSelectedSeverity(level.id)}
            >
              <View style={[styles.severityDot, { backgroundColor: level.color }]} />
              <Text
                variant="bodySmall"
                color={level.color}
                weight={selectedSeverity === level.id ? 'semibold' : 'regular'}
              >
                {level.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Input
          label="Description"
          placeholder="Provide detailed information about the incident"
          multiline
          numberOfLines={4}
          textAlignVertical="top"
          style={styles.textArea}
          value={description}
          onChangeText={setDescription}
        />
        
        <Text variant="bodySmall" weight="medium" style={styles.label}>
          Attach Photos (Optional)
        </Text>
        
        <View style={styles.photoContainer}>
          <TouchableOpacity
            style={[styles.addPhotoButton, { borderColor: colors.border }]}
            onPress={handleAddPhoto}
          >
            <Camera size={24} color={Colors.gray[500]} />
            <Text variant="bodySmall" color={Colors.gray[500]}>
              Add Photo
            </Text>
          </TouchableOpacity>
          
          {/* This would display thumbnails of added photos */}
        </View>
        
        <Button
          title="Submit Report"
          size="lg"
          onPress={handleSubmit}
          loading={loading}
          disabled={!isFormValid}
          fullWidth
          style={styles.submitButton}
        />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingBottom: Spacing.xxxl,
  },
  label: {
    marginBottom: Spacing.xs,
  },
  typeContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.md,
    gap: Spacing.sm,
  },
  typeButton: {
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    borderColor: Colors.gray[300],
    marginBottom: Spacing.sm,
  },
  severityContainer: {
    flexDirection: 'row',
    marginBottom: Spacing.lg,
    gap: Spacing.md,
  },
  severityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: Spacing.sm,
    paddingHorizontal: Spacing.md,
    borderRadius: Layout.borderRadius.md,
    borderWidth: 1,
    gap: 6,
  },
  severityDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
  },
  textArea: {
    height: 120,
    textAlignVertical: 'top',
    paddingTop: Spacing.sm,
  },
  photoContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: Spacing.xl,
  },
  addPhotoButton: {
    width: 100,
    height: 100,
    borderWidth: 1,
    borderStyle: 'dashed',
    borderRadius: Layout.borderRadius.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: Spacing.sm,
    marginBottom: Spacing.sm,
  },
  photoThumbnail: {
    width: 100,
    height: 100,
    borderRadius: Layout.borderRadius.md,
    marginRight: Spacing.sm,
    marginBottom: Spacing.sm,
    position: 'relative',
  },
  removePhotoButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.error[500],
    justifyContent: 'center',
    alignItems: 'center',
  },
  submitButton: {
    marginTop: Spacing.md,
  },
});