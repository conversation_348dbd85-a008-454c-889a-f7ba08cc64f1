import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, TouchableOpacity, Switch, Image, Alert } from 'react-native';
import { useRouter } from 'expo-router';
import { User, Phone, Mail, Calendar, Settings, MapPin, Shield, LogOut, ChevronRight, CreditCard as Edit2 } from 'lucide-react-native';
import Text from '@/components/ui/Text';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Colors from '@/constants/Colors';
import Spacing from '@/constants/Spacing';
import Layout from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

// Sample driver profile data
const DRIVER_PROFILE = {
  id: 'DRV-2023',
  name: '<PERSON>',
  avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=300',
  phone: '+251 91 234 5678',
  email: '<EMAIL>',
  licenseNumber: 'LIC-78901-ETH',
  licenseExpiry: 'May 15, 2025',
  address: 'Bole Sub-City, Addis Ababa',
  emergencyContact: {
    name: 'Abeba Tesfaye',
    relation: 'Spouse',
    phone: '+251 91 876 5432',
  },
  joinDate: 'March 10, 2020',
  totalTrips: 1452,
  totalDistance: '35,245 km',
  rating: 4.8,
  preferredRoutes: ['R23', 'R24', 'R27'],
};

export default function ProfileScreen() {
  const router = useRouter();
  const { colorScheme, colors } = useColorScheme();
  const [locationSharing, setLocationSharing] = useState(true);
  const [notifications, setNotifications] = useState(true);
  
  const handleLogout = () => {
    Alert.alert(
      "Logout",
      "Are you sure you want to logout?",
      [
        { text: "Cancel", style: "cancel" },
        { 
          text: "Logout", 
          onPress: () => {
            router.replace('/(auth)');
          }
        }
      ]
    );
  };
  
  const handleEditProfile = () => {
    Alert.alert(
      "Edit Profile",
      "This functionality is not available in the demo version.",
      [{ text: "OK" }]
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.contentContainer}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            {DRIVER_PROFILE.avatar ? (
              <Image
                source={{ uri: DRIVER_PROFILE.avatar }}
                style={styles.avatar}
                resizeMode="cover"
              />
            ) : (
              <View style={[styles.avatarPlaceholder, { backgroundColor: Colors.primary[500] }]}>
                <User size={40} color={Colors.white} />
              </View>
            )}
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: colors.card }]}
              onPress={handleEditProfile}
            >
              <Edit2 size={16} color={Colors.primary[500]} />
            </TouchableOpacity>
          </View>
          
          <Text variant="h3" weight="bold" style={styles.name}>
            {DRIVER_PROFILE.name}
          </Text>
          
          <Text variant="body" color={Colors.gray[500]}>
            Driver ID: {DRIVER_PROFILE.id}
          </Text>
        </View>
        
        <Card variant="elevated" style={styles.infoCard}>
          <Text variant="h4" weight="semibold" style={styles.cardTitle}>
            Contact Information
          </Text>
          
          <View style={styles.infoRow}>
            <Phone size={20} color={Colors.gray[500]} />
            <Text variant="body" style={styles.infoText}>
              {DRIVER_PROFILE.phone}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <Mail size={20} color={Colors.gray[500]} />
            <Text variant="body" style={styles.infoText}>
              {DRIVER_PROFILE.email}
            </Text>
          </View>
          
          <View style={styles.infoRow}>
            <MapPin size={20} color={Colors.gray[500]} />
            <Text variant="body" style={styles.infoText}>
              {DRIVER_PROFILE.address}
            </Text>
          </View>
        </Card>
        
        <Card variant="elevated" style={styles.infoCard}>
          <Text variant="h4" weight="semibold" style={styles.cardTitle}>
            Driver License
          </Text>
          
          <View style={styles.licenseInfo}>
            <View>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                License Number
              </Text>
              <Text variant="body" weight="medium">
                {DRIVER_PROFILE.licenseNumber}
              </Text>
            </View>
            
            <View>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                Expiry Date
              </Text>
              <Text 
                variant="body" 
                weight="medium"
                color={new Date(DRIVER_PROFILE.licenseExpiry) < new Date() ? Colors.error[500] : colors.text}
              >
                {DRIVER_PROFILE.licenseExpiry}
              </Text>
            </View>
          </View>
        </Card>
        
        <Card variant="elevated" style={styles.infoCard}>
          <Text variant="h4" weight="semibold" style={styles.cardTitle}>
            Emergency Contact
          </Text>
          
          <Text variant="body" weight="medium">
            {DRIVER_PROFILE.emergencyContact.name}
          </Text>
          
          <Text variant="bodySmall" color={Colors.gray[500]}>
            {DRIVER_PROFILE.emergencyContact.relation}
          </Text>
          
          <Text variant="body" style={styles.emergencyPhone}>
            {DRIVER_PROFILE.emergencyContact.phone}
          </Text>
        </Card>
        
        <Card variant="elevated" style={styles.infoCard}>
          <Text variant="h4" weight="semibold" style={styles.cardTitle}>
            Statistics
          </Text>
          
          <View style={styles.statsGrid}>
            <View style={styles.statItem}>
              <Text variant="h3" weight="bold" color={Colors.primary[500]}>
                {DRIVER_PROFILE.totalTrips}
              </Text>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                Total Trips
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text variant="h3" weight="bold" color={Colors.primary[500]}>
                {DRIVER_PROFILE.totalDistance}
              </Text>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                Distance
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text variant="h3" weight="bold" color={Colors.primary[500]}>
                {DRIVER_PROFILE.rating}
              </Text>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                Rating
              </Text>
            </View>
            
            <View style={styles.statItem}>
              <Text variant="h3" weight="bold" color={Colors.primary[500]}>
                {DRIVER_PROFILE.joinDate.split(' ')[0]}
              </Text>
              <Text variant="bodySmall" color={Colors.gray[500]}>
                Since {DRIVER_PROFILE.joinDate.split(' ')[1]} {DRIVER_PROFILE.joinDate.split(' ')[2]}
              </Text>
            </View>
          </View>
        </Card>
        
        <Card variant="elevated" style={styles.infoCard}>
          <Text variant="h4" weight="semibold" style={styles.cardTitle}>
            Settings
          </Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingLabel}>
              <MapPin size={20} color={Colors.gray[500]} />
              <Text variant="body" style={styles.settingText}>
                Location Sharing
              </Text>
            </View>
            <Switch
              value={locationSharing}
              onValueChange={setLocationSharing}
              trackColor={{ false: Colors.gray[300], true: Colors.primary[300] }}
              thumbColor={locationSharing ? Colors.primary[500] : Colors.gray[100]}
              ios_backgroundColor={Colors.gray[300]}
            />
          </View>
          
          <View style={styles.settingRow}>
            <View style={styles.settingLabel}>
              <Bell size={20} color={Colors.gray[500]} />
              <Text variant="body" style={styles.settingText}>
                Push Notifications
              </Text>
            </View>
            <Switch
              value={notifications}
              onValueChange={setNotifications}
              trackColor={{ false: Colors.gray[300], true: Colors.primary[300] }}
              thumbColor={notifications ? Colors.primary[500] : Colors.gray[100]}
              ios_backgroundColor={Colors.gray[300]}
            />
          </View>
          
          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingLabel}>
              <Shield size={20} color={Colors.gray[500]} />
              <Text variant="body" style={styles.settingText}>
                Privacy & Security
              </Text>
            </View>
            <ChevronRight size={20} color={Colors.gray[400]} />
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.settingItem}>
            <View style={styles.settingLabel}>
              <Settings size={20} color={Colors.gray[500]} />
              <Text variant="body" style={styles.settingText}>
                App Settings
              </Text>
            </View>
            <ChevronRight size={20} color={Colors.gray[400]} />
          </TouchableOpacity>
        </Card>
        
        <Button
          title="Logout"
          variant="outline"
          icon={<LogOut size={20} color={Colors.error[500]} />}
          style={styles.logoutButton}
          textStyle={{ color: Colors.error[500] }}
          onPress={handleLogout}
        />
      </ScrollView>
    </View>
  );
}

// Custom Bell icon component
function Bell({ size, color }) {
  return (
    <View>
      <Calendar size={size} color={color} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: Spacing.md,
    paddingBottom: Spacing.xxl,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  avatarContainer: {
    marginBottom: Spacing.md,
    position: 'relative',
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  avatarPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  editButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.gray[200],
  },
  name: {
    marginBottom: 4,
  },
  infoCard: {
    marginBottom: Spacing.lg,
  },
  cardTitle: {
    marginBottom: Spacing.md,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.sm,
  },
  infoText: {
    marginLeft: Spacing.sm,
  },
  licenseInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  emergencyPhone: {
    marginTop: Spacing.sm,
    color: Colors.primary[500],
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -Spacing.sm,
  },
  statItem: {
    width: '50%',
    padding: Spacing.sm,
    marginBottom: Spacing.md,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  settingLabel: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingText: {
    marginLeft: Spacing.sm,
  },
  logoutButton: {
    marginTop: Spacing.md,
  },
});